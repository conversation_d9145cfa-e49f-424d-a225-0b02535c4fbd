"""
用户模型单元测试
"""

import pytest
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.models.user import User, UserSession
from tests.utils.factories import UserFactory, AdminUserFactory


@pytest.mark.unit
@pytest.mark.db
class TestUserModel:
    """用户模型测试类"""
    
    def test_create_user(self, db: Session):
        """测试创建用户"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "full_name": "Test User",
            "hashed_password": "hashed_password",
            "role": "annotator"
        }
        
        user = User(**user_data)
        db.add(user)
        db.commit()
        db.refresh(user)
        
        assert user.id is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.full_name == "Test User"
        assert user.role == "annotator"
        assert user.is_active is True
        assert user.is_verified is False
        assert user.created_at is not None
        assert user.updated_at is not None
    
    def test_user_factory(self, db: Session):
        """测试用户工厂"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        assert user.id is not None
        assert user.username is not None
        assert user.email is not None
        assert user.role == "annotator"
        assert user.is_active is True
    
    def test_admin_user_factory(self, db: Session):
        """测试管理员用户工厂"""
        AdminUserFactory._meta.sqlalchemy_session = db
        admin = AdminUserFactory()
        
        assert admin.id is not None
        assert admin.role == "admin"
        assert admin.username.startswith("admin")
    
    def test_username_unique_constraint(self, db: Session):
        """测试用户名唯一约束"""
        UserFactory._meta.sqlalchemy_session = db
        
        # 创建第一个用户
        user1 = UserFactory(username="duplicate_user")
        
        # 尝试创建相同用户名的用户
        with pytest.raises(IntegrityError):
            user2 = UserFactory(username="duplicate_user")
            db.commit()
    
    def test_email_unique_constraint(self, db: Session):
        """测试邮箱唯一约束"""
        UserFactory._meta.sqlalchemy_session = db
        
        # 创建第一个用户
        user1 = UserFactory(email="<EMAIL>")
        
        # 尝试创建相同邮箱的用户
        with pytest.raises(IntegrityError):
            user2 = UserFactory(email="<EMAIL>")
            db.commit()
    
    def test_user_repr(self, db: Session):
        """测试用户字符串表示"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory(username="testuser")
        
        repr_str = repr(user)
        assert "testuser" in repr_str
        assert str(user.id) in repr_str
    
    def test_user_role_validation(self, db: Session):
        """测试用户角色验证"""
        valid_roles = ["admin", "annotator", "reviewer", "viewer"]
        
        for role in valid_roles:
            user = User(
                username=f"user_{role}",
                email=f"{role}@example.com",
                hashed_password="password",
                role=role
            )
            db.add(user)
        
        db.commit()
        
        # 验证所有用户都被成功创建
        users = db.query(User).all()
        assert len(users) == len(valid_roles)
    
    def test_user_default_values(self, db: Session):
        """测试用户默认值"""
        user = User(
            username="defaultuser",
            email="<EMAIL>",
            hashed_password="password"
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        
        assert user.is_active is True
        assert user.is_verified is False
        assert user.role == "annotator"  # 默认角色
    
    def test_user_timestamps(self, db: Session):
        """测试用户时间戳"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        original_updated_at = user.updated_at
        
        # 更新用户
        user.full_name = "Updated Name"
        db.commit()
        db.refresh(user)
        
        assert user.updated_at > original_updated_at
    
    def test_user_relationships(self, db: Session):
        """测试用户关系"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        # 测试用户可以访问相关的会话、创建的知识点等
        # 这里可以添加更多关系测试
        assert hasattr(user, 'sessions')
        assert hasattr(user, 'created_knowledge_points')
        assert hasattr(user, 'created_questions')


@pytest.mark.unit
@pytest.mark.db
class TestUserSessionModel:
    """用户会话模型测试类"""
    
    def test_create_user_session(self, db: Session):
        """测试创建用户会话"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        session_data = {
            "user_id": user.id,
            "session_token": "test_session_token",
            "ip_address": "***********",
            "user_agent": "Test User Agent",
            "is_active": True
        }
        
        session = UserSession(**session_data)
        db.add(session)
        db.commit()
        db.refresh(session)
        
        assert session.id is not None
        assert session.user_id == user.id
        assert session.session_token == "test_session_token"
        assert session.ip_address == "***********"
        assert session.is_active is True
        assert session.created_at is not None
    
    def test_user_session_relationship(self, db: Session):
        """测试用户会话关系"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        session = UserSession(
            user_id=user.id,
            session_token="test_token",
            ip_address="127.0.0.1"
        )
        db.add(session)
        db.commit()
        db.refresh(session)
        
        # 测试关系
        assert session.user == user
        assert session in user.sessions
    
    def test_session_token_unique_constraint(self, db: Session):
        """测试会话令牌唯一约束"""
        UserFactory._meta.sqlalchemy_session = db
        user1 = UserFactory()
        user2 = UserFactory()
        
        # 创建第一个会话
        session1 = UserSession(
            user_id=user1.id,
            session_token="duplicate_token",
            ip_address="127.0.0.1"
        )
        db.add(session1)
        db.commit()
        
        # 尝试创建相同令牌的会话
        with pytest.raises(IntegrityError):
            session2 = UserSession(
                user_id=user2.id,
                session_token="duplicate_token",
                ip_address="*********"
            )
            db.add(session2)
            db.commit()
    
    def test_session_default_values(self, db: Session):
        """测试会话默认值"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        session = UserSession(
            user_id=user.id,
            session_token="test_token"
        )
        db.add(session)
        db.commit()
        db.refresh(session)
        
        assert session.is_active is True
        assert session.expires_at is not None
