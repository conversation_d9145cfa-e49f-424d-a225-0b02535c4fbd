"""
知识点模型单元测试
"""

import pytest
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.models.knowledge import KnowledgePoint, PrerequisiteRelation
from tests.utils.factories import (
    KnowledgePointFactory, 
    PrerequisiteRelationFactory, 
    UserFactory,
    create_test_knowledge_hierarchy
)


@pytest.mark.unit
@pytest.mark.db
class TestKnowledgePointModel:
    """知识点模型测试类"""
    
    def test_create_knowledge_point(self, db: Session):
        """测试创建知识点"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        kp_data = {
            "name": "测试知识点",
            "code": "TEST_KP_001",
            "description": "这是一个测试知识点",
            "subject": "数学",
            "grade": "高中",
            "difficulty": 3,
            "is_leaf": True,
            "path": "TEST_KP_001",
            "created_by": user.id,
            "updated_by": user.id
        }
        
        kp = KnowledgePoint(**kp_data)
        db.add(kp)
        db.commit()
        db.refresh(kp)
        
        assert kp.kp_id is not None
        assert kp.name == "测试知识点"
        assert kp.code == "TEST_KP_001"
        assert kp.subject == "数学"
        assert kp.grade == "高中"
        assert kp.difficulty == 3
        assert kp.is_leaf is True
        assert kp.path == "TEST_KP_001"
        assert kp.created_at is not None
        assert kp.updated_at is not None
    
    def test_knowledge_point_factory(self, db: Session):
        """测试知识点工厂"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        kp = KnowledgePointFactory()
        
        assert kp.kp_id is not None
        assert kp.name is not None
        assert kp.code is not None
        assert kp.subject in ["数学", "物理", "化学", "生物"]
        assert kp.grade in ["初中", "高中", "大学"]
        assert 1 <= kp.difficulty <= 5
        assert kp.is_leaf is True
    
    def test_code_unique_constraint(self, db: Session):
        """测试知识点编码唯一约束"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        
        # 创建第一个知识点
        kp1 = KnowledgePointFactory(code="DUPLICATE_CODE")
        
        # 尝试创建相同编码的知识点
        with pytest.raises(IntegrityError):
            kp2 = KnowledgePointFactory(code="DUPLICATE_CODE")
            db.commit()
    
    def test_knowledge_point_hierarchy(self, db: Session):
        """测试知识点层次结构"""
        hierarchy = create_test_knowledge_hierarchy(db, "数学")
        root, child1, child2 = hierarchy
        
        # 验证层次关系
        assert root.parent_id is None
        assert child1.parent_id == root.kp_id
        assert child2.parent_id == root.kp_id
        
        # 验证路径
        assert root.path == root.code
        assert child1.path == f"{root.code}.{child1.code}"
        assert child2.path == f"{root.code}.{child2.code}"
        
        # 验证叶子节点状态
        assert root.is_leaf is False
        assert child1.is_leaf is True
        assert child2.is_leaf is True
    
    def test_knowledge_point_repr(self, db: Session):
        """测试知识点字符串表示"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        kp = KnowledgePointFactory(name="测试知识点", code="TEST_001")
        
        repr_str = repr(kp)
        assert "测试知识点" in repr_str
        assert "TEST_001" in repr_str
    
    def test_knowledge_point_relationships(self, db: Session):
        """测试知识点关系"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        kp = KnowledgePointFactory()
        
        # 测试知识点可以访问相关的题目、先修关系等
        assert hasattr(kp, 'questions')
        assert hasattr(kp, 'prerequisite_relations_as_pre')
        assert hasattr(kp, 'prerequisite_relations_as_post')
        assert hasattr(kp, 'creator')
        assert hasattr(kp, 'updater')
    
    def test_knowledge_point_path_generation(self, db: Session):
        """测试知识点路径生成"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        # 创建根知识点
        root = KnowledgePoint(
            name="根知识点",
            code="ROOT",
            path="ROOT",
            created_by=user.id,
            updated_by=user.id
        )
        db.add(root)
        db.commit()
        db.refresh(root)
        
        # 创建子知识点
        child = KnowledgePoint(
            name="子知识点",
            code="CHILD",
            parent_id=root.kp_id,
            path=f"{root.path}.CHILD",
            created_by=user.id,
            updated_by=user.id
        )
        db.add(child)
        db.commit()
        db.refresh(child)
        
        # 创建孙知识点
        grandchild = KnowledgePoint(
            name="孙知识点",
            code="GRANDCHILD",
            parent_id=child.kp_id,
            path=f"{child.path}.GRANDCHILD",
            created_by=user.id,
            updated_by=user.id
        )
        db.add(grandchild)
        db.commit()
        db.refresh(grandchild)
        
        assert root.path == "ROOT"
        assert child.path == "ROOT.CHILD"
        assert grandchild.path == "ROOT.CHILD.GRANDCHILD"


@pytest.mark.unit
@pytest.mark.db
class TestPrerequisiteRelationModel:
    """先修关系模型测试类"""
    
    def test_create_prerequisite_relation(self, db: Session):
        """测试创建先修关系"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        pre_kp = KnowledgePointFactory()
        post_kp = KnowledgePointFactory()
        user = UserFactory()
        
        relation_data = {
            "pre_kp_id": pre_kp.kp_id,
            "post_kp_id": post_kp.kp_id,
            "relation_type": "prerequisite",
            "strength": 0.8,
            "created_by": user.id,
            "updated_by": user.id
        }
        
        relation = PrerequisiteRelation(**relation_data)
        db.add(relation)
        db.commit()
        db.refresh(relation)
        
        assert relation.relation_id is not None
        assert relation.pre_kp_id == pre_kp.kp_id
        assert relation.post_kp_id == post_kp.kp_id
        assert relation.relation_type == "prerequisite"
        assert relation.strength == 0.8
        assert relation.created_at is not None
        assert relation.updated_at is not None
    
    def test_prerequisite_relation_factory(self, db: Session):
        """测试先修关系工厂"""
        PrerequisiteRelationFactory._meta.sqlalchemy_session = db
        relation = PrerequisiteRelationFactory()
        
        assert relation.relation_id is not None
        assert relation.pre_kp_id is not None
        assert relation.post_kp_id is not None
        assert relation.relation_type == "prerequisite"
        assert 0.5 <= relation.strength <= 1.0
    
    def test_prerequisite_relation_unique_constraint(self, db: Session):
        """测试先修关系唯一约束"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        pre_kp = KnowledgePointFactory()
        post_kp = KnowledgePointFactory()
        user = UserFactory()
        
        # 创建第一个先修关系
        relation1 = PrerequisiteRelation(
            pre_kp_id=pre_kp.kp_id,
            post_kp_id=post_kp.kp_id,
            created_by=user.id,
            updated_by=user.id
        )
        db.add(relation1)
        db.commit()
        
        # 尝试创建相同的先修关系
        with pytest.raises(IntegrityError):
            relation2 = PrerequisiteRelation(
                pre_kp_id=pre_kp.kp_id,
                post_kp_id=post_kp.kp_id,
                created_by=user.id,
                updated_by=user.id
            )
            db.add(relation2)
            db.commit()
    
    def test_prerequisite_relation_relationships(self, db: Session):
        """测试先修关系的关系"""
        PrerequisiteRelationFactory._meta.sqlalchemy_session = db
        relation = PrerequisiteRelationFactory()
        
        # 测试关系
        assert relation.pre_kp is not None
        assert relation.post_kp is not None
        assert relation.creator is not None
        assert relation.updater is not None
        
        # 验证关系的正确性
        assert relation.pre_kp.kp_id == relation.pre_kp_id
        assert relation.post_kp.kp_id == relation.post_kp_id
    
    def test_prerequisite_relation_validation(self, db: Session):
        """测试先修关系验证"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        kp = KnowledgePointFactory()
        user = UserFactory()
        
        # 测试不能自己作为自己的先修
        with pytest.raises(IntegrityError):
            relation = PrerequisiteRelation(
                pre_kp_id=kp.kp_id,
                post_kp_id=kp.kp_id,
                created_by=user.id,
                updated_by=user.id
            )
            db.add(relation)
            db.commit()
    
    def test_prerequisite_strength_range(self, db: Session):
        """测试先修关系强度范围"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        pre_kp = KnowledgePointFactory()
        post_kp = KnowledgePointFactory()
        user = UserFactory()
        
        # 测试有效的强度值
        valid_strengths = [0.0, 0.5, 0.8, 1.0]
        
        for strength in valid_strengths:
            relation = PrerequisiteRelation(
                pre_kp_id=pre_kp.kp_id,
                post_kp_id=post_kp.kp_id,
                strength=strength,
                created_by=user.id,
                updated_by=user.id
            )
            db.add(relation)
            db.commit()
            db.refresh(relation)
            
            assert relation.strength == strength
            
            # 清理
            db.delete(relation)
            db.commit()
