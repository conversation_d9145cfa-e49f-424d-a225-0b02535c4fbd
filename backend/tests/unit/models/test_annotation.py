"""
标注模型单元测试
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.models.annotation import AnnotationTask, AnnotationLog
from tests.utils.factories import (
    AnnotationTaskFactory,
    AnnotationLogFactory,
    UserFactory,
    QuestionFactory
)


@pytest.mark.unit
@pytest.mark.db
class TestAnnotationTaskModel:
    """标注任务模型测试类"""
    
    def test_create_annotation_task(self, db: Session):
        """测试创建标注任务"""
        UserFactory._meta.sqlalchemy_session = db
        creator = UserFactory()
        assignee = UserFactory()
        
        task_data = {
            "title": "测试标注任务",
            "description": "这是一个测试标注任务",
            "task_type": "knowledge_point_mapping",
            "priority": "medium",
            "status": "pending",
            "deadline": datetime.utcnow() + timedelta(days=7),
            "created_by": creator.id,
            "assigned_to": assignee.id
        }
        
        task = AnnotationTask(**task_data)
        db.add(task)
        db.commit()
        db.refresh(task)
        
        assert task.task_id is not None
        assert task.title == "测试标注任务"
        assert task.description == "这是一个测试标注任务"
        assert task.task_type == "knowledge_point_mapping"
        assert task.priority == "medium"
        assert task.status == "pending"
        assert task.deadline is not None
        assert task.created_by == creator.id
        assert task.assigned_to == assignee.id
        assert task.created_at is not None
        assert task.updated_at is not None
    
    def test_annotation_task_factory(self, db: Session):
        """测试标注任务工厂"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        task = AnnotationTaskFactory()
        
        assert task.task_id is not None
        assert task.title is not None
        assert task.description is not None
        assert task.task_type in ["knowledge_point_mapping", "difficulty_rating", "quality_review"]
        assert task.priority in ["low", "medium", "high", "urgent"]
        assert task.status == "pending"
        assert task.deadline is not None
    
    def test_annotation_task_types(self, db: Session):
        """测试标注任务类型"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        
        task_types = [
            "knowledge_point_mapping",
            "difficulty_rating", 
            "quality_review",
            "content_validation",
            "answer_verification"
        ]
        
        for task_type in task_types:
            task = AnnotationTaskFactory(task_type=task_type)
            assert task.task_type == task_type
    
    def test_annotation_task_priorities(self, db: Session):
        """测试标注任务优先级"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        
        priorities = ["low", "medium", "high", "urgent"]
        
        for priority in priorities:
            task = AnnotationTaskFactory(priority=priority)
            assert task.priority == priority
    
    def test_annotation_task_statuses(self, db: Session):
        """测试标注任务状态"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        
        statuses = ["pending", "in_progress", "completed", "cancelled", "rejected"]
        
        for status in statuses:
            task = AnnotationTaskFactory(status=status)
            assert task.status == status
    
    def test_annotation_task_repr(self, db: Session):
        """测试标注任务字符串表示"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        task = AnnotationTaskFactory(title="测试任务")
        
        repr_str = repr(task)
        assert "测试任务" in repr_str
        assert str(task.task_id) in repr_str
    
    def test_annotation_task_relationships(self, db: Session):
        """测试标注任务关系"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        task = AnnotationTaskFactory()
        
        # 测试标注任务可以访问相关的用户、日志等
        assert hasattr(task, 'creator')
        assert hasattr(task, 'assignee')
        assert hasattr(task, 'logs')
        assert hasattr(task, 'questions')
    
    def test_annotation_task_deadline_validation(self, db: Session):
        """测试标注任务截止时间验证"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        # 测试未来时间
        future_deadline = datetime.utcnow() + timedelta(days=7)
        task = AnnotationTask(
            title="未来截止时间任务",
            task_type="knowledge_point_mapping",
            deadline=future_deadline,
            created_by=user.id,
            assigned_to=user.id
        )
        db.add(task)
        db.commit()
        db.refresh(task)
        
        assert task.deadline == future_deadline
    
    def test_annotation_task_metadata(self, db: Session):
        """测试标注任务元数据"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        
        metadata = {
            "instructions": "请仔细标注知识点",
            "examples": ["示例1", "示例2"],
            "quality_criteria": {
                "accuracy": 0.9,
                "completeness": 0.8
            }
        }
        
        task = AnnotationTaskFactory(metadata=metadata)
        
        assert task.metadata == metadata
        assert isinstance(task.metadata, dict)
        assert task.metadata["instructions"] == "请仔细标注知识点"
    
    def test_annotation_task_progress_tracking(self, db: Session):
        """测试标注任务进度跟踪"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        
        task = AnnotationTaskFactory(
            total_items=100,
            completed_items=30,
            progress=0.3
        )
        
        assert task.total_items == 100
        assert task.completed_items == 30
        assert task.progress == 0.3


@pytest.mark.unit
@pytest.mark.db
class TestAnnotationLogModel:
    """标注日志模型测试类"""
    
    def test_create_annotation_log(self, db: Session):
        """测试创建标注日志"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        task = AnnotationTaskFactory()
        user = UserFactory()
        
        log_data = {
            "task_id": task.task_id,
            "annotator_id": user.id,
            "action": "start",
            "content": "开始标注任务",
            "metadata": {"start_time": datetime.utcnow().isoformat()}
        }
        
        log = AnnotationLog(**log_data)
        db.add(log)
        db.commit()
        db.refresh(log)
        
        assert log.log_id is not None
        assert log.task_id == task.task_id
        assert log.annotator_id == user.id
        assert log.action == "start"
        assert log.content == "开始标注任务"
        assert log.metadata is not None
        assert log.created_at is not None
    
    def test_annotation_log_factory(self, db: Session):
        """测试标注日志工厂"""
        AnnotationLogFactory._meta.sqlalchemy_session = db
        log = AnnotationLogFactory()
        
        assert log.log_id is not None
        assert log.task_id is not None
        assert log.annotator_id is not None
        assert log.action in ["start", "submit", "review", "approve", "reject"]
        assert log.content is not None
        assert log.created_at is not None
    
    def test_annotation_log_actions(self, db: Session):
        """测试标注日志动作"""
        AnnotationLogFactory._meta.sqlalchemy_session = db
        
        actions = ["start", "submit", "review", "approve", "reject", "update", "comment"]
        
        for action in actions:
            log = AnnotationLogFactory(action=action)
            assert log.action == action
    
    def test_annotation_log_relationships(self, db: Session):
        """测试标注日志关系"""
        AnnotationLogFactory._meta.sqlalchemy_session = db
        log = AnnotationLogFactory()
        
        # 测试关系
        assert log.task is not None
        assert log.annotator is not None
        assert log.task.task_id == log.task_id
        assert log.annotator.id == log.annotator_id
    
    def test_annotation_log_metadata(self, db: Session):
        """测试标注日志元数据"""
        AnnotationLogFactory._meta.sqlalchemy_session = db
        
        metadata = {
            "duration": 1800,  # 30分钟
            "changes": {
                "knowledge_points": ["KP001", "KP002"],
                "difficulty": 3
            },
            "quality_score": 0.85
        }
        
        log = AnnotationLogFactory(
            action="submit",
            metadata=metadata
        )
        
        assert log.metadata == metadata
        assert isinstance(log.metadata, dict)
        assert log.metadata["duration"] == 1800
    
    def test_annotation_log_chronological_order(self, db: Session):
        """测试标注日志时间顺序"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        task = AnnotationTaskFactory()
        user = UserFactory()
        
        # 创建多个日志条目
        actions = ["start", "update", "submit", "review", "approve"]
        logs = []
        
        for i, action in enumerate(actions):
            log = AnnotationLog(
                task_id=task.task_id,
                annotator_id=user.id,
                action=action,
                content=f"执行动作: {action}"
            )
            db.add(log)
            db.commit()
            db.refresh(log)
            logs.append(log)
        
        # 验证时间顺序
        for i in range(1, len(logs)):
            assert logs[i].created_at >= logs[i-1].created_at
    
    def test_annotation_log_task_cascade(self, db: Session):
        """测试标注日志任务级联删除"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        AnnotationLogFactory._meta.sqlalchemy_session = db
        
        task = AnnotationTaskFactory()
        log = AnnotationLogFactory(task_id=task.task_id)
        
        task_id = task.task_id
        log_id = log.log_id
        
        # 删除任务
        db.delete(task)
        db.commit()
        
        # 验证日志也被删除（如果设置了级联删除）
        remaining_log = db.query(AnnotationLog).filter(
            AnnotationLog.log_id == log_id
        ).first()
        
        # 根据实际的级联设置来验证
        # 如果设置了级联删除，remaining_log应该为None
        # 如果没有设置级联删除，可能需要手动删除或者会有外键约束错误
